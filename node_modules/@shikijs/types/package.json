{"name": "@shikijs/types", "type": "module", "version": "1.29.2", "description": "Type definitions for Shiki", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/shikijs/shiki#readme", "repository": {"type": "git", "url": "git+https://github.com/shikijs/shiki.git", "directory": "packages/types"}, "bugs": "https://github.com/shikijs/shiki/issues", "keywords": ["shiki"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.mts", "files": ["dist"], "dependencies": {"@shikijs/vscode-textmate": "^10.0.1", "@types/hast": "^3.0.4"}, "scripts": {"build": "unbuild", "dev": "unbuild --stub"}}