import { create } from 'zustand';
import { persist } from 'zustand/middleware';

type Provider = {
  id: string;
  name: string;
  isConfigured: boolean;
  apiKey?: string;
  baseURL?: string;
  settings?: Record<string, unknown>;
};

type Model = {
  id: string;
  name: string;
  description?: string;
  contextLength?: number;
  capabilities?: string[];
};

interface SettingsState {
  // Legacy support
  providers: Provider[];
  currentProvider?: Provider;
  currentModel?: Model;
  activeProvider: string;

  // New provider-specific settings
  // OpenAI
  openaiApiKey: string;
  openaiBaseUrl: string;
  openaiModel: string;

  // Anthropic
  anthropicApiKey: string;
  anthropicBaseUrl: string;
  anthropicModel: string;

  // Gemini
  geminiApiKey: string;
  geminiBaseUrl: string;
  geminiModel: string;

  // Groq
  groqApiKey: string;
  groqBaseUrl: string;
  groqModel: string;

  // DeepSeek
  deepseekApiKey: string;
  deepseekBaseUrl: string;
  deepseekModel: string;

  // Mistral
  mistralApiKey: string;
  mistralBaseUrl: string;
  mistralModel: string;

  // Moonshot
  moonshotApiKey: string;
  moonshotBaseUrl: string;
  moonshotModel: string;

  // OpenRouter
  openrouterApiKey: string;
  openrouterBaseUrl: string;
  openrouterModel: string;

  // Perplexity
  perplexityApiKey: string;
  perplexityBaseUrl: string;
  perplexityModel: string;

  // Qwen
  qwenApiKey: string;
  qwenBaseUrl: string;
  qwenModel: string;

  // Together
  togetherApiKey: string;
  togetherBaseUrl: string;
  togetherModel: string;

  // Vertex
  vertexApiKey: string;
  vertexBaseUrl: string;
  vertexModel: string;

  // XAI
  xaiApiKey: string;
  xaiBaseUrl: string;
  xaiModel: string;

  // Ollama
  ollamaBaseUrl: string;
  ollamaModel: string;

  // LM Studio
  lmstudioBaseUrl: string;
  lmstudioModel: string;

  // Actions
  setActiveProvider: (provider: string) => void;
  setProvider: (id: string) => void;
  setModel: (id: string) => void;
  updateProviderKey: (id: string, key: string) => void;

  // OpenAI setters
  setOpenaiApiKey: (key: string) => void;
  setOpenaiBaseUrl: (url: string) => void;
  setOpenaiModel: (model: string) => void;

  // Anthropic setters
  setAnthropicApiKey: (key: string) => void;
  setAnthropicBaseUrl: (url: string) => void;
  setAnthropicModel: (model: string) => void;
}