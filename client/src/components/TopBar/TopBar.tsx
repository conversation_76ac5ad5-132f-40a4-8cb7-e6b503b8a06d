import React, { useMemo } from 'react';
import { useSettingsStore } from '../../stores/settingsStore';
import { useModalStore } from '../../stores/modalStore';
import { ProviderStatusIndicator } from '../ui/ProviderStatusIndicator';
import { Plus, History, Settings } from 'lucide-react';

export const TopBar: React.FC = () => {
  const { currentProvider, currentModel } = useSettingsStore();
  const { openModal } = useModalStore();

  const display = useMemo(() => {
    if (!currentProvider) return 'Select Provider';
    if (!currentModel) return `${currentProvider.name}:Select Model`;
    return `${currentProvider.name}:${currentModel.name}`;
  }, [currentProvider, currentModel]);

  return (
    <header className="flex items-center justify-between px-3 py-2 border-b border-adobe-border bg-adobe-bg-secondary">
      <ProviderStatusIndicator />
      <button
        onClick={() => openModal('provider')}
        className="text-sm font-semibold text-adobe-text-primary hover:text-adobe-accent truncate max-w-[170px]"
        title="Change provider / model"
      >
        {display}
      </button>
      <div className="flex items-center gap-2">
        <button onClick={() => openModal('chat-history')} title="History">
          <History size={16} />
        </button>
        <button onClick={() => openModal('settings')} title="Settings">
          <Settings size={16} />
        </button>
        <button onClick={() => { /* new chat handled inside store */ }} title="New chat">
          <Plus size={16} />
        </button>
      </div>
    </header>
  );
};

export default TopBar;