import React from 'react';
import { Message } from '../../stores/chatStore';
import { ShikiCodeBlock } from './ShikiCodeBlock';
import { User, Bot } from 'lucide-react';

export const ChatMessage: React.FC<{ message: Message }> = ({ message }) => {
  const isUser = message.role === 'user';

  return (
    <div className={`flex gap-2 ${isUser ? 'justify-end' : 'justify-start'}`}>
      {!isUser && <Bot size={20} className="mt-1 text-adobe-accent" />}
      <div
        className={`max-w-[85%] rounded px-3 py-2 text-sm leading-relaxed ${
          isUser
            ? 'bg-adobe-accent text-white'
            : 'bg-adobe-bg-secondary text-adobe-text-primary'
        }`}
      >
        <ShikiCodeBlock content={message.content} />
      </div>
      {isUser && <User size={20} className="mt-1 text-adobe-text-secondary" />}
    </div>
  );
};