import React, { useEffect, useRef } from 'react';
import { useChatStore } from '../../stores/chatStore';
import { ChatMessage } from './ChatMessage';

export const ChatMessages: React.FC = () => {
  const { messages, isLoading } = useChatStore();
  const bottomRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    bottomRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, isLoading]);

  return (
    <div className="flex-1 overflow-y-auto px-3 py-2 space-y-4">
      {messages.length === 0 && (
        <div className="flex flex-col items-center justify-center h-full text-adobe-text-secondary">
          <span className="text-sm">Ask anything…</span>
        </div>
      )}
      {messages.map((msg) => (
        <ChatMessage key={msg.id} message={msg} />
      ))}
      {isLoading && (
        <div className="flex items-center gap-2 text-adobe-text-secondary text-sm">
          <span>AI is thinking…</span>
        </div>
      )}
      <div ref={bottomRef} />
    </div>
  );
};