// src/components/Chat/InputArea.tsx
import React, { useState, useRef, useCallback } from 'react';
import { useChatStore } from '../stores/chatStore';
import { Paperclip, Send, Mic, Loader2 } from 'lucide-react';

interface InputAreaProps {
  onAttachFile?: () => void;
  onVoiceInput?: () => void;
  onContextReference?: () => void;
}

const InputArea: React.FC<InputAreaProps> = React.memo(({
  onAttachFile,
  onVoiceInput,
  onContextReference
}) => {
  const [input, setInput] = useState('');
  const [isComposing, setIsComposing] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const { addMessage, isLoading, setLoading, currentSession, createNewSession } = useChatStore();

  const maxChars = 4000;
  const isEmpty = !input.trim();

  const resize = useCallback(() => {
    const el = textareaRef.current;
    if (!el) return;
    el.style.height = 'auto';
    el.style.height = `${Math.min(el.scrollHeight, 200)}px`;
  }, []);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value);
    resize();
  }, [resize]);

  const handleSend = useCallback(async () => {
    const text = input.trim();
    if (!text || isLoading) return;

    setInput('');
    resize();

    try {
      setLoading(true);
      if (!currentSession) createNewSession();
      addMessage({ content: text, role: 'user' });

      // TODO: replace with real API call
      setTimeout(() => {
        addMessage({ content: `Echo: ${text}`, role: 'assistant' });
        setLoading(false);
      }, 1000);
    } catch (err) {
      setInput(text);
      setLoading(false);
    }
  }, [input, isLoading, currentSession, addMessage, setLoading, createNewSession, resize]);

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === 'Enter' && !e.shiftKey && !isComposing) {
        e.preventDefault();
        handleSend();
      }
    },
    [handleSend, isComposing]
  );

  return (
    <div className="px-3 py-2 border-t border-adobe-border">
      <div className="relative flex items-end bg-adobe-bg-secondary rounded border border-adobe-border focus-within:border-adobe-accent">
        <textarea
          ref={textareaRef}
          rows={1}
          maxLength={maxChars}
          value={input}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          onCompositionStart={() => setIsComposing(true)}
          onCompositionEnd={() => setIsComposing(false)}
          placeholder="Type a message…"
          className="flex-1 resize-none bg-transparent text-sm p-2 outline-none placeholder:text-adobe-text-secondary"
          style={{ minHeight: '36px' }}
        />
        <div className="flex items-center space-x-1 pr-2 pb-1">
          <button
            onClick={onAttachFile}
            className="text-adobe-text-secondary hover:text-adobe-text-primary transition"
            title="Attach file"
          >
            <Paperclip size={16} />
          </button>
          <button
            onClick={onVoiceInput}
            className="text-adobe-text-secondary hover:text-adobe-text-primary transition"
            title="Voice input"
          >
            <Mic size={16} />
          </button>
          <button
            onClick={handleSend}
            disabled={isEmpty || isLoading}
            className="text-adobe-accent disabled:text-adobe-text-secondary/50 transition"
            title="Send"
          >
            {isLoading ? <Loader2 size={16} className="animate-spin" /> : <Send size={16} />}
          </button>
        </div>
      </div>
      <p className="text-xs text-adobe-text-secondary mt-1 flex justify-between">
        <span>
          {input.length}/{maxChars}
        </span>
        <span>Shift + Enter for new line</span>
      </p>
    </div>
  );
});

export default InputArea;