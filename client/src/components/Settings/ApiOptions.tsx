import React from 'react';
import { useSettingsStore } from '../stores/settingsStore';
import { AnthropicProvider } from './providers/AnthropicProvider';
import { OpenAIProvider } from './providers/OpenAIProvider';
import { OllamaProvider } from './providers/OllamaProvider';
import { LMStudioProvider } from './providers/LMStudioProvider';
import { DeepSeekProvider } from './providers/DeepSeekProvider';
import { GeminiProvider } from './providers/GeminiProvider';
import { GroqProvider } from './providers/GroqProvider';
import { MistralProvider } from './providers/MistralProvider';
import { MoonshotProvider } from './providers/MoonshotProvider';
import { OpenRouterProvider } from './providers/OpenRouterProvider';
import { PerplexityProvider } from './providers/PerplexityProvider';
import { QwenProvider } from './providers/QwenProvider';
import { TogetherProvider } from './providers/TogetherProvider';
import { VertexProvider } from './providers/VertexProvider';
import { XAIProvider } from './providers/XAIProvider';

interface ApiOptionsProps {
  showModelOptions?: boolean;
  apiErrorMessage?: string;
  modelIdErrorMessage?: string;
}

export const ApiOptions: React.FC<ApiOptionsProps> = ({
  showModelOptions = true,
  apiErrorMessage,
  modelIdErrorMessage,
}) => {
  const { activeProvider, setActiveProvider } = useSettingsStore();

  const handleProviderChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setActiveProvider(e.target.value);
  };

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
          API Provider
        </label>
        <select
          value={activeProvider}
          onChange={handleProviderChange}
          className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-3 py-2 text-adobe-text-primary focus-within:border-adobe-accent outline-none"
        >
          <option value="openai">OpenAI</option>
          <option value="anthropic">Anthropic</option>
          <option value="gemini">Google Gemini</option>
          <option value="groq">Groq</option>
          <option value="deepseek">DeepSeek</option>
          <option value="mistral">Mistral</option>
          <option value="moonshot">Moonshot AI</option>
          <option value="openrouter">OpenRouter</option>
          <option value="perplexity">Perplexity</option>
          <option value="qwen">Alibaba Qwen</option>
          <option value="together">Together AI</option>
          <option value="vertex">Google Vertex AI</option>
          <option value="xai">xAI</option>
          <option value="ollama">Ollama</option>
          <option value="lmstudio">LM Studio</option>
        </select>
      </div>

      {/* Provider-specific configuration */}
      {activeProvider === "anthropic" && (
        <AnthropicProvider showModelOptions={showModelOptions} />
      )}
      {activeProvider === "openai" && (
        <OpenAIProvider showModelOptions={showModelOptions} />
      )}
      {activeProvider === "gemini" && (
        <GeminiProvider showModelOptions={showModelOptions} />
      )}
      {activeProvider === "groq" && (
        <GroqProvider showModelOptions={showModelOptions} />
      )}
      {activeProvider === "deepseek" && (
        <DeepSeekProvider showModelOptions={showModelOptions} />
      )}
      {activeProvider === "mistral" && (
        <MistralProvider showModelOptions={showModelOptions} />
      )}
      {activeProvider === "moonshot" && (
        <MoonshotProvider showModelOptions={showModelOptions} />
      )}
      {activeProvider === "openrouter" && (
        <OpenRouterProvider showModelOptions={showModelOptions} />
      )}
      {activeProvider === "perplexity" && (
        <PerplexityProvider showModelOptions={showModelOptions} />
      )}
      {activeProvider === "qwen" && (
        <QwenProvider showModelOptions={showModelOptions} />
      )}
      {activeProvider === "together" && (
        <TogetherProvider showModelOptions={showModelOptions} />
      )}
      {activeProvider === "vertex" && (
        <VertexProvider showModelOptions={showModelOptions} />
      )}
      {activeProvider === "xai" && (
        <XAIProvider showModelOptions={showModelOptions} />
      )}
      {activeProvider === "ollama" && (
        <OllamaProvider showModelOptions={showModelOptions} />
      )}
      {activeProvider === "lmstudio" && (
        <LMStudioProvider showModelOptions={showModelOptions} />
      )}

      {apiErrorMessage && (
        <div className="text-adobe-error text-sm">
          {apiErrorMessage}
        </div>
      )}
      {modelIdErrorMessage && (
        <div className="text-adobe-error text-sm">
          {modelIdErrorMessage}
        </div>
      )}
    </div>
  );
};
