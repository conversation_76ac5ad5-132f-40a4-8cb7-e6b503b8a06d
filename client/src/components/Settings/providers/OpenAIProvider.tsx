import React from 'react';
import { useSettingsStore } from '../../stores/settingsStore';
import { Api<PERSON>eyField } from '../common/ApiKeyField';
import { BaseUrlField } from '../common/BaseUrlField';
import { ModelSelector } from '../common/ModelSelector';

const openaiModels = {
  'gpt-4o': {
    name: 'GPT-4o',
    description: 'Most advanced multimodal model',
    contextWindow: 128000,
    maxTokens: 16384,
  },
  'gpt-4o-mini': {
    name: 'GPT-4o Mini',
    description: 'Fast and efficient',
    contextWindow: 128000,
    maxTokens: 16384,
  },
  'gpt-4-turbo': {
    name: 'GPT-4 Turbo',
    description: 'Latest GPT-4 model',
    contextWindow: 128000,
    maxTokens: 4096,
  },
  'gpt-4': {
    name: 'GPT-4',
    description: 'Most capable model',
    contextWindow: 8192,
    maxTokens: 4096,
  },
  'gpt-3.5-turbo': {
    name: 'GPT-3.5 Turbo',
    description: 'Fast and cost-effective',
    contextWindow: 16385,
    maxTokens: 4096,
  },
};

interface OpenAIProviderProps {
  showModelOptions?: boolean;
}

export const OpenAIProvider: React.FC<OpenAIProviderProps> = ({
  showModelOptions = true,
}) => {
  const { 
    openaiApiKey, 
    openaiBaseUrl, 
    openaiModel,
    setOpenaiApiKey,
    setOpenaiBaseUrl,
    setOpenaiModel,
  } = useSettingsStore();

  const handleModelChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setOpenaiModel(e.target.value);
  };

  return (
    <div className="space-y-4">
      <ApiKeyField
        initialValue={openaiApiKey}
        onChange={setOpenaiApiKey}
        providerName="OpenAI"
        signupUrl="https://platform.openai.com/api-keys"
      />
      
      <BaseUrlField
        initialValue={openaiBaseUrl}
        onChange={setOpenaiBaseUrl}
        placeholder="Default: https://api.openai.com/v1"
        label="Use custom base URL"
      />

      {showModelOptions && (
        <ModelSelector
          models={openaiModels}
          selectedModelId={openaiModel}
          onChange={handleModelChange}
          label="Model"
        />
      )}
    </div>
  );
};
