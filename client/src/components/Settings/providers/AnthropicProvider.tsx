import React from 'react';
import { useSettingsStore } from '../../stores/settingsStore';
import { Api<PERSON>eyField } from '../common/ApiKeyField';
import { BaseUrlField } from '../common/BaseUrlField';
import { ModelSelector } from '../common/ModelSelector';

const anthropicModels = {
  'claude-3-5-sonnet-20241022': {
    name: 'Claude 3.5 Sonnet',
    description: 'Most intelligent model',
    contextWindow: 200000,
    maxTokens: 8192,
  },
  'claude-3-5-haiku-20241022': {
    name: 'Claude 3.5 Haiku',
    description: 'Fastest model',
    contextWindow: 200000,
    maxTokens: 8192,
  },
  'claude-3-opus-20240229': {
    name: 'Claude 3 Opus',
    description: 'Most powerful model',
    contextWindow: 200000,
    maxTokens: 4096,
  },
  'claude-3-sonnet-20240229': {
    name: 'Claude 3 Sonnet',
    description: 'Balanced performance',
    contextWindow: 200000,
    maxTokens: 4096,
  },
  'claude-3-haiku-20240307': {
    name: 'Claude 3 Haiku',
    description: 'Fast and efficient',
    contextWindow: 200000,
    maxTokens: 4096,
  },
};

interface AnthropicProviderProps {
  showModelOptions?: boolean;
}

export const AnthropicProvider: React.FC<AnthropicProviderProps> = ({
  showModelOptions = true,
}) => {
  const { 
    anthropicApiKey, 
    anthropicBaseUrl, 
    anthropicModel,
    setAnthropicApiKey,
    setAnthropicBaseUrl,
    setAnthropicModel,
  } = useSettingsStore();

  const handleModelChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setAnthropicModel(e.target.value);
  };

  return (
    <div className="space-y-4">
      <ApiKeyField
        initialValue={anthropicApiKey}
        onChange={setAnthropicApiKey}
        providerName="Anthropic"
        signupUrl="https://console.anthropic.com/settings/keys"
      />
      
      <BaseUrlField
        initialValue={anthropicBaseUrl}
        onChange={setAnthropicBaseUrl}
        placeholder="Default: https://api.anthropic.com"
        label="Use custom base URL"
      />

      {showModelOptions && (
        <ModelSelector
          models={anthropicModels}
          selectedModelId={anthropicModel}
          onChange={handleModelChange}
          label="Model"
        />
      )}
    </div>
  );
};
