import React from 'react';

interface ModelInfo {
  name: string;
  description?: string;
  contextWindow?: number;
  maxTokens?: number;
}

interface ModelSelectorProps {
  models: Record<string, ModelInfo>;
  selectedModelId: string | undefined;
  onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  label?: string;
}

export const ModelSelector: React.FC<ModelSelectorProps> = ({
  models,
  selectedModelId,
  onChange,
  label = "Model",
}) => {
  return (
    <div className="space-y-2">
      <label className="block text-sm font-semibold text-adobe-text-primary">
        {label}
      </label>
      <select
        value={selectedModelId || ""}
        onChange={onChange}
        className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-3 py-2 text-adobe-text-primary focus-within:border-adobe-accent outline-none"
      >
        <option value="">Select a model...</option>
        {Object.entries(models).map(([modelId, modelInfo]) => (
          <option key={modelId} value={modelId}>
            {modelInfo.name || modelId}
          </option>
        ))}
      </select>
    </div>
  );
};
