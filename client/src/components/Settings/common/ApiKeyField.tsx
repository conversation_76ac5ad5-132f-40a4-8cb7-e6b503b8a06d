import React, { useState, useEffect } from 'react';

interface ApiKeyFieldProps {
  initialValue: string;
  onChange: (value: string) => void;
  providerName: string;
  signupUrl?: string;
  placeholder?: string;
  helpText?: string;
}

export const ApiKeyField: React.FC<ApiKeyFieldProps> = ({
  initialValue,
  onChange,
  providerName,
  signupUrl,
  placeholder = "Enter API Key...",
  helpText,
}) => {
  const [localValue, setLocalValue] = useState(initialValue);

  // Debounced update
  useEffect(() => {
    const timer = setTimeout(() => {
      onChange(localValue);
    }, 300);

    return () => clearTimeout(timer);
  }, [localValue, onChange]);

  // Update local value when initial value changes
  useEffect(() => {
    setLocalValue(initialValue);
  }, [initialValue]);

  return (
    <div className="space-y-2">
      <label className="block text-sm font-semibold text-adobe-text-primary">
        {providerName} API Key
      </label>
      <input
        type="password"
        value={localValue}
        onChange={(e) => setLocalValue(e.target.value)}
        placeholder={placeholder}
        className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-3 py-2 text-adobe-text-primary placeholder:text-adobe-text-secondary focus-within:border-adobe-accent outline-none"
      />
      <div className="text-xs text-adobe-text-secondary">
        {helpText || "This key is stored locally and only used to make API requests from this extension."}
        {!localValue && signupUrl && (
          <>
            {" "}
            <a 
              href={signupUrl} 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-adobe-accent hover:text-adobe-text-primary"
            >
              You can get a{/^[aeiou]/i.test(providerName) ? "n" : ""} {providerName} API key by signing up here.
            </a>
          </>
        )}
      </div>
    </div>
  );
};
