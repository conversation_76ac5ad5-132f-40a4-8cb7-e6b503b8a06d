import React, { useState, useEffect } from 'react';

interface BaseUrlFieldProps {
  initialValue: string;
  onChange: (value: string) => void;
  placeholder?: string;
  label?: string;
}

export const BaseUrlField: React.FC<BaseUrlFieldProps> = ({
  initialValue,
  onChange,
  placeholder = "Enter base URL...",
  label = "Base URL",
}) => {
  const [localValue, setLocalValue] = useState(initialValue);

  // Debounced update
  useEffect(() => {
    const timer = setTimeout(() => {
      onChange(localValue);
    }, 300);

    return () => clearTimeout(timer);
  }, [localValue, onChange]);

  // Update local value when initial value changes
  useEffect(() => {
    setLocalValue(initialValue);
  }, [initialValue]);

  return (
    <div className="space-y-2">
      <label className="block text-sm font-semibold text-adobe-text-primary">
        {label}
      </label>
      <input
        type="url"
        value={localValue}
        onChange={(e) => setLocalValue(e.target.value)}
        placeholder={placeholder}
        className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-3 py-2 text-adobe-text-primary placeholder:text-adobe-text-secondary focus-within:border-adobe-accent outline-none"
      />
    </div>
  );
};
