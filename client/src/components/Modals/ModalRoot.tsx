import React from 'react';
import { useModalStore } from '../stores/modalStore';
import { ProviderModal } from './ProviderModal';

export const ModalRoot: React.FC = () => {
  const { modal } = useModalStore();

  if (!modal) return null;

  switch (modal) {
    case 'provider':
      return <ProviderModal />;
    case 'settings':
      // TODO: Implement SettingsModal
      return null;
    case 'chat-history':
      // TODO: Implement ChatHistoryModal
      return null;
    default:
      return null;
  }
};
