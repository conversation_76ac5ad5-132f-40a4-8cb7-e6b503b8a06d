import React from 'react';
import { useSettingsStore } from '../stores/settingsStore';
import { useModalStore } from '../stores/modalStore';
import { OpenAIProvider } from '../../providers/openai';
import { OllamaProvider } from '../../providers/ollama';
import { LMStudioProvider } from '../../providers/lmstudio';
import { AnthropicProvider } from '../../providers/anthropic';

const providerMap = {
  openai: OpenAIProvider,
  anthropic: AnthropicProvider,
  ollama: OllamaProvider,
  lmstudio: LMStudioProvider,
};

export const ProviderModal: React.FC = () => {
  const { activeProvider } = useSettingsStore();
  const { closeModal } = useModalStore();
  const ProviderComponent = providerMap[activeProvider as keyof typeof providerMap];

  if (!ProviderComponent) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-adobe-bg border border-adobe-border rounded-md w-[380px] max-h-[550px] overflow-y-auto p-4">
        <ProviderComponent onClose={closeModal} />
        <button onClick={closeModal} className="mt-4 w-full bg-adobe-accent text-white py-2 rounded">
          Close
        </button>
      </div>
    </div>
  );
};