import React, { useEffect, useState } from 'react';
import { useSettingsStore } from '../stores/settingsStore';

const isMac = /Mac/.test(navigator.platform);
const defaultBaseURL = isMac ? 'http://localhost:11434' : 'http://localhost:11434';

export const OllamaProvider: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const { updateProviderKey } = useSettingsStore();
  const [baseURL, setBaseURL] = useState(defaultBaseURL);
  const [models, setModels] = useState<{ id: string; name: string }[]>([]);

  const loadModels = async () => {
    try {
      const res = await fetch(`${baseURL}/api/tags`, { mode: 'cors' });
      const data = await res.json();
      setModels(data.models.map((m: any) => ({ id: m.name, name: m.name })));
    } catch {
      setModels([]);
    }
  };

  useEffect(() => {
    loadModels();
  }, [baseURL]);

  return (
    <div className="space-y-3 text-sm">
      <h3 className="font-bold text-lg">Ollama</h3>
      <input
        type="text"
        placeholder={defaultBaseURL}
        value={baseURL}
        onChange={(e) => setBaseURL(e.target.value)}
        className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
      />
      {models.length ? (
        <select
          className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
          onChange={(e) => updateProviderKey('ollama', baseURL, e.target.value)}
        >
          {models.map((m) => (
            <option key={m.id} value={m.id}>{m.name}</option>
          ))}
        </select>
      ) : (
        <p className="text-xs text-adobe-text-secondary">No models found at {baseURL}</p>
      )}
      <button
        onClick={() => { updateProviderKey('ollama', baseURL); onClose(); }}
        className="w-full bg-adobe-accent text-white rounded py-1"
      >
        Save & Close
      </button>
    </div>
  );
};