import React, { useState } from 'react';
import { useSettingsStore } from '../stores/settingsStore';

export const QwenProvider: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const { updateProviderKey } = useSettingsStore();
  const [key, setKey] = useState('');
  const [line, setLine] = useState<'china' | 'international'>('china');
  const models = [
    { id: 'qwen-max', name: '<PERSON>wen Max' },
    { id: 'qwen-turbo', name: 'Qwen Turbo' },
  ];

  return (
    <div className="space-y-3 text-sm">
      <h3 className="font-bold text-lg">Qwen</h3>
      <select
        className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1 mb-2"
        value={line}
        onChange={(e) => setLine(e.target.value as 'china' | 'international')}
      >
        <option value="china">China API</option>
        <option value="international">International API</option>
      </select>
      <input
        type="password"
        placeholder="sk-..."
        value={key}
        onChange={(e) => setKey(e.target.value)}
        className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
      />
      <select
        className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
        onChange={(e) => updateProviderKey('qwen', key, e.target.value)}
      >
        {models.map((m) => (
          <option key={m.id} value={m.id}>{m.name}</option>
        ))}
      </select>
      <button
        onClick={() => { updateProviderKey('qwen', key); onClose(); }}
        className="w-full bg-adobe-accent text-white rounded py-1"
      >
        Save & Close
      </button>
    </div>
  );
};