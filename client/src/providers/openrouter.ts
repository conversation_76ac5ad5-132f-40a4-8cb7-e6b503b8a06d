import React, { useState } from 'react';
import { useSettingsStore } from '../stores/settingsStore';

export const OpenRouterProvider: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const { updateProviderKey } = useSettingsStore();
  const [key, setKey] = useState('');
  const models = [
    { id: 'openai/gpt-4', name: 'GPT-4 via OR' },
    { id: 'anthropic/claude-3-5-sonnet', name: 'Claude 3.5 via OR' },
  ];

  return (
    <div className="space-y-3 text-sm">
      <h3 className="font-bold text-lg">OpenRouter</h3>
      <input
        type="password"
        placeholder="sk-or-..."
        value={key}
        onChange={(e) => setKey(e.target.value)}
        className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
      />
      <select
        className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
        onChange={(e) => updateProviderKey('openrouter', key, e.target.value)}
      >
        {models.map((m) => (
          <option key={m.id} value={m.id}>{m.name}</option>
        ))}
      </select>
      <button
        onClick={() => { updateProviderKey('openrouter', key); onClose(); }}
        className="w-full bg-adobe-accent text-white rounded py-1"
      >
        Save & Close
      </button>
    </div>
  );
};