import React, { useEffect, useState } from 'react';
import { useSettingsStore } from '../stores/settingsStore';

export const OpenAIProvider: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const { updateProviderKey } = useSettingsStore();
  const [key, setKey] = useState('');
  const [models, setModels] = useState<{ id: string; name: string }[]>([]);

  const loadModels = async () => {
    const res = await fetch('https://api.openai.com/v1/models', {
      headers: { Authorization: `Bearer ${key}` },
      mode: 'no-cors',
    });
    const data = await res.json();
    setModels(data.data.map((m: any) => ({ id: m.id, name: m.id })));
  };

  useEffect(() => { if (key) loadModels(); }, [key]);

  return (
    <div className="space-y-3 text-sm">
      <h3 className="font-bold text-lg">OpenAI</h3>
      <input
        type="password"
        placeholder="sk-..."
        value={key}
        onChange={(e) => setKey(e.target.value)}
        className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
      />
      <select
        className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
        onChange={(e) => updateProviderKey('openai', key, e.target.value)}
      >
        {models.map((m) => (
          <option key={m.id} value={m.id}>{m.name}</option>
        ))}
      </select>
      <button
        onClick={() => { updateProviderKey('openai', key); onClose(); }}
        className="w-full bg-adobe-accent text-white rounded py-1"
      >
        Save & Close
      </button>
    </div>
  );
};