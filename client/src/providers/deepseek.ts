import React, { useState } from 'react';
import { useSettingsStore } from '../stores/settingsStore';

export const DeepSeekProvider: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const { updateProviderKey } = useSettingsStore();
  const [key, setKey] = useState('');
  const models = [
    { id: 'deepseek-chat', name: 'DeepSeek Chat' },
    { id: 'deepseek-coder', name: 'DeepSeek Coder' },
  ];

  return (
    <div className="space-y-3 text-sm">
      <h3 className="font-bold text-lg">DeepSeek</h3>
      <input
        type="password"
        placeholder="sk-..."
        value={key}
        onChange={(e) => setKey(e.target.value)}
        className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
      />
      <select
        className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
        onChange={(e) => updateProviderKey('deepseek', key, e.target.value)}
      >
        {models.map((m) => (
          <option key={m.id} value={m.id}>{m.name}</option>
        ))}
      </select>
      <button
        onClick={() => { updateProviderKey('deepseek', key); onClose(); }}
        className="w-full bg-adobe-accent text-white rounded py-1"
      >
        Save & Close
      </button>
    </div>
  );
};