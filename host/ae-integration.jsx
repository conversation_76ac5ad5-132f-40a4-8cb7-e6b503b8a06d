/**
 * SahAI CEP Extension V2 - ExtendScript Main File
 * This file handles communication between the CEP panel and Adobe applications
 */

// Global namespace for SahAI ExtendScript functions
var SahAI = SahAI || {};

/**
 * Initialize the ExtendScript environment
 */
SahAI.init = function() {
    try {
        // Set up error handling
        $.level = 1; // Enable debugging
        
        // Log initialization
        $.writeln("SahAI ExtendScript initialized successfully");
        
        return {
            success: true,
            message: "ExtendScript initialized",
            version: "2.0.0"
        };
    } catch (error) {
        $.writeln("Error initializing SahAI ExtendScript: " + error.toString());
        return {
            success: false,
            message: error.toString()
        };
    }
};

/**
 * Get application information
 */
SahAI.getAppInfo = function() {
    try {
        return {
            success: true,
            data: {
                name: app.name,
                version: app.version,
                locale: app.locale,
                build: app.build || "Unknown"
            }
        };
    } catch (error) {
        return {
            success: false,
            message: error.toString()
        };
    }
};

/**
 * Execute code in the host application
 * @param {string} code - The code to execute
 * @param {string} language - The programming language (for context)
 */
SahAI.executeCode = function(code, language) {
    try {
        var result;
        
        switch (language.toLowerCase()) {
            case 'javascript':
            case 'extendscript':
                // Execute ExtendScript code
                result = eval(code);
                break;
                
            case 'applescript':
                // Execute AppleScript (macOS only)
                if ($.os.indexOf("Mac") !== -1) {
                    result = app.doScript(code, ScriptLanguage.APPLESCRIPT_LANGUAGE);
                } else {
                    throw new Error("AppleScript is only supported on macOS");
                }
                break;
                
            case 'vbscript':
                // Execute VBScript (Windows only)
                if ($.os.indexOf("Win") !== -1) {
                    result = app.doScript(code, ScriptLanguage.VISUAL_BASIC);
                } else {
                    throw new Error("VBScript is only supported on Windows");
                }
                break;
                
            default:
                throw new Error("Unsupported language: " + language);
        }
        
        return {
            success: true,
            result: result ? result.toString() : "Code executed successfully",
            language: language
        };
    } catch (error) {
        return {
            success: false,
            message: error.toString(),
            language: language
        };
    }
};

/**
 * Get document information
 */
SahAI.getDocumentInfo = function() {
    try {
        if (!app.activeDocument) {
            return {
                success: false,
                message: "No active document"
            };
        }
        
        var doc = app.activeDocument;
        return {
            success: true,
            data: {
                name: doc.name,
                path: doc.fullName ? doc.fullName.toString() : "Unsaved",
                saved: doc.saved,
                modified: doc.modified || false
            }
        };
    } catch (error) {
        return {
            success: false,
            message: error.toString()
        };
    }
};

/**
 * Show alert dialog
 * @param {string} message - The message to display
 * @param {string} title - The dialog title
 */
SahAI.showAlert = function(message, title) {
    try {
        title = title || "SahAI";
        alert(message, title);
        return {
            success: true,
            message: "Alert displayed"
        };
    } catch (error) {
        return {
            success: false,
            message: error.toString()
        };
    }
};

/**
 * Log message to ExtendScript console
 * @param {string} message - The message to log
 * @param {string} level - Log level (info, warn, error)
 */
SahAI.log = function(message, level) {
    try {
        level = level || "info";
        var timestamp = new Date().toISOString();
        var logMessage = "[" + timestamp + "] [" + level.toUpperCase() + "] " + message;
        
        $.writeln(logMessage);
        
        return {
            success: true,
            message: "Logged: " + message
        };
    } catch (error) {
        return {
            success: false,
            message: error.toString()
        };
    }
};

/**
 * Get system information
 */
SahAI.getSystemInfo = function() {
    try {
        return {
            success: true,
            data: {
                os: $.os,
                version: $.version,
                buildDate: $.buildDate,
                locale: $.locale,
                memoryUsage: $.memCache
            }
        };
    } catch (error) {
        return {
            success: false,
            message: error.toString()
        };
    }
};

// Initialize SahAI when script loads
SahAI.init();
