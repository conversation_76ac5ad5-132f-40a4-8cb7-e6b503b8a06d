#!/usr/bin/env node
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const ROOT = path.resolve(__dirname);
const DIST = path.join(ROOT, 'dist');

// Files/folders that **Vite does NOT bundle** but CEP needs
const CEP_ASSETS = [
  'CSXS',               // manifest.xml
  'host',               // ae-integration.jsx
  'icons',              // icon-16.png / icon-32.png
  '.debug',             // enables debug mode
];

async function copyDir(src, dest) {
  await fs.promises.mkdir(dest, { recursive: true });
  const entries = await fs.promises.readdir(src, { withFileTypes: true });

  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);

    if (entry.isDirectory()) {
      await copyDir(srcPath, destPath);
    } else {
      await fs.promises.copyFile(srcPath, destPath);
    }
  }
}

async function main() {
  console.log('🚀 Copying CEP assets to dist/');
  await fs.promises.mkdir(DIST, { recursive: true });

  for (const dir of CEP_ASSETS) {
    const src = path.join(ROOT, dir);
    const dest = path.join(DIST, dir);
    if (fs.existsSync(src)) {
      await copyDir(src, dest);
      console.log(`  ✅ copied ${dir}`);
    } else {
      console.log(`  ⚠️  ${dir} not found – skipped`);
    }
  }

  // Optional: remove .DS_Store / Thumbs.db files
  try {
    await fs.promises.unlink(path.join(DIST, '.DS_Store'));
  } catch (e) { /* ignore */ }
  try {
    await fs.promises.unlink(path.join(DIST, 'Thumbs.db'));
  } catch (e) { /* ignore */ }

  console.log('🎉 dist/ ready for CEP');
}

main().catch(console.error);